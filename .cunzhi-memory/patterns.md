# 常用模式和最佳实践

- 全屏模式最佳实践：1. 使用useEffect控制document.body和documentElement的overflow-hidden类来禁用滚动 2. 直接在Select组件的SelectContent上设置z-[150]高z-index而不是通过复杂CSS选择器 3. 依赖Radix UI的Portal机制确保下拉菜单渲染到body末尾 4. 组件卸载时清理滚动控制状态
- 全局模态框系统已实现：基于Zustand状态管理，支持类型安全的模态框管理，包含LoginModal、PricingModal、FeedbackModal等组件，使用next/dynamic懒加载优化性能，支持响应式设计，提供便捷hooks和兼容性迁移方案
- Modal组件开发教训：1.不要搞复杂的新旧版本兼容，直接用新版本 2.翻译内容要放在专用文件如i18n/pages/pricing/而不是通用messages/ 3.使用const page = await getPricingPage(locale)获取数据和翻译，而不是useTranslations 4.组件要简洁，避免过度设计
- AI模型翻译系统最佳实践：1)模型配置中设置translationKey字段指向翻译文件中的key 2)在model-manager.ts中保留translationKey信息传递给前端 3)前端useAIGeneration.ts中统一进行翻译处理，使用translationKey匹配翻译内容 4)关键是filterModels函数必须使用已翻译的allModels而不是重新获取未翻译数据 5)翻译文件结构为i18n/pages/AiModelTranslation/{locale}.json，key格式为grsai.model-id 6)避免在后端和前端同时进行翻译处理导致冲突
