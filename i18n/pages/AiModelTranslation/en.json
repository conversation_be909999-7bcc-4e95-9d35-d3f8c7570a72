{"common": {"parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate, affects response length"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}, "aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for storage and access"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "variants": {"description": "Generation count", "tooltip": "Select the number of images to generate"}, "size": {"description": "Image size", "tooltip": "Select the specific dimensions for generated images"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "duration": {"description": "Video duration", "tooltip": "Select the duration of the generated video"}, "resolution": {"description": "Video resolution", "tooltip": "Select the resolution of the generated video"}, "fps": {"description": "Frame rate", "tooltip": "Select the frame rate of the video"}, "quality": {"description": "Generation quality", "tooltip": "Select the quality level for generation"}, "motion_intensity": {"description": "Motion intensity", "tooltip": "Control the motion amplitude, 1 for static, 10 for intense motion"}}, "parameterGroups": {"basic": "Basic Settings", "advanced": "Advanced Settings", "expert": "Expert Settings"}, "options": {"aspectRatio": {"1:1": {"label": "Square (1:1)", "description": "Perfect for social media avatars"}, "16:9": {"label": "Landscape (16:9)", "description": "Perfect for video covers"}, "9:16": {"label": "Portrait (9:16)", "description": "Perfect for mobile wallpapers"}, "4:3": {"label": "Standard (4:3)", "description": "Classic photo ratio"}, "3:2": {"label": "Photo (3:2)", "description": "Camera default ratio"}, "21:9": {"label": "Ultra-wide (21:9)", "description": "Cinematic ratio"}, "2:3": {"label": "Portrait Photo (2:3)", "description": "Portrait camera ratio"}, "4:5": {"label": "Instagram (4:5)", "description": "Instagram portrait"}, "5:4": {"label": "Classic Landscape (5:4)", "description": "Classic landscape ratio"}, "3:4": {"label": "Standard Portrait (3:4)", "description": "Standard portrait ratio"}}, "cdn": {"global": {"label": "Global CDN", "description": "Use global CDN acceleration"}, "zh": {"label": "China CDN", "description": "Use China CDN acceleration"}}}, "ui": {"range": "Range", "minValue": "Min value", "maxValue": "Max value", "step": "Step"}}, "models": {"grsai.gemini-2-5-pro": {"name": "Gemini 2.5 Pro", "description": "Advanced conversational model, suitable for complex tasks and professional use"}, "grsai.gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "Fast conversational model, quick response and high efficiency"}, "grsai.gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "Lightweight conversational model, low cost and basic functionality"}, "grsai.gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o lightweight version, balanced performance and cost"}, "grsai.o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini full-featured version with vision and multimodal capabilities", "parameters": {"uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}}}, "grsai.gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o complete version with all advanced features and multimodal support", "parameters": {"uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}}}, "grsai.flux-pro-1.1": {"name": "Flux Pro 1.1", "description": "Professional image generation with Flux v1.1 technology"}, "grsai.gpt-4o-image": {"name": "GPT-4o Image", "description": "High-quality image generation using GPT-4o architecture"}, "grsai.flux-kontext-pro": {"name": "Flux Kontext Pro", "description": "Context-aware professional-grade image generation"}, "grsai.sora-image": {"name": "Sora Image", "description": "Advanced image generation model based on Sora technology"}, "grsai.flux-pro-1.1-ultra": {"name": "Flux Pro 1.1 Ultra", "description": "Enhanced Flux Pro ultra-high quality image generation"}, "grsai.flux-kontext-max": {"name": "Flux Kontext Max", "description": "Highest quality context-aware image generation"}, "grsai.veo3-fast": {"name": "Veo3 Fast", "description": "Fast video generation with Veo3 technology, quick results", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}}}, "grsai.veo3-pro": {"name": "Veo3 Pro", "description": "Professional video generation with advanced Veo3 capabilities", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}}}}}