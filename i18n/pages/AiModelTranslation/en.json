{"models": {"grsai.gemini-2-5-pro": {"name": "Gemini 2.5 Pro111", "description": "Advanced conversational model, suitable for complex tasks and professional use", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate, affects response length"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "Fast conversational model, quick response and high efficiency", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "Lightweight conversational model, low cost and basic functionality", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}}}, "grsai.gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o lightweight version, balanced performance and cost", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini full-featured version with vision and multimodal capabilities", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o complete version with all advanced features and multimodal support", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.flux-pro-1.1": {"name": "Flux Pro 1.1", "description": "Professional image generation with Flux v1.1 technology", "parameters": {"aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.gpt-4o-image": {"name": "GPT-4o Image11", "description": "High-quality image generation using GPT-4o architecture", "parameters": {"variants": {"description": "Generation count", "tooltip": "Select the number of images to generate (1-2)"}, "size": {"description": "Image size", "tooltip": "Select the specific dimensions for generated images"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.flux-kontext-pro": {"name": "Flux Kontext Pro", "description": "Context-aware professional-grade image generation", "parameters": {"aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.sora-image": {"name": "Sora Image", "description": "Advanced image generation model based on Sora technology", "parameters": {"variants": {"description": "Generation count", "tooltip": "Select the number of images to generate (1-2)"}, "size": {"description": "Image size", "tooltip": "Select the specific dimensions for generated images"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.flux-pro-1.1-ultra": {"name": "Flux Pro 1.1 Ultra", "description": "Enhanced Flux Pro ultra-high quality image generation", "parameters": {"aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.flux-kontext-max": {"name": "Flux Kontext Max", "description": "Highest quality context-aware image generation", "parameters": {"aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for image storage and access"}}}, "grsai.veo3-fast": {"name": "Veo3 Fast", "description": "Fast video generation with Veo3 technology, quick results", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}, "duration": {"description": "Video duration", "tooltip": "Select the duration of the generated video"}, "resolution": {"description": "Video resolution", "tooltip": "Select the resolution of the generated video"}, "fps": {"description": "Frame rate", "tooltip": "Select the frame rate of the video"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for video storage and access"}}}, "grsai.veo3-pro": {"name": "Veo3 Pro", "description": "Professional video generation with advanced Veo3 capabilities", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}, "duration": {"description": "Video duration", "tooltip": "Select the duration of the generated video"}, "resolution": {"description": "Video resolution", "tooltip": "Select the resolution of the generated video"}, "fps": {"description": "Frame rate", "tooltip": "Select the frame rate of the video"}, "quality": {"description": "Generation quality", "tooltip": "Select the quality level for video generation"}, "motion_intensity": {"description": "Motion intensity", "tooltip": "Control the motion amplitude in the video, 1 for static, 10 for intense motion"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for video storage and access"}}}}}