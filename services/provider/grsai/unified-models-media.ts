/**
 * GRSAI 统一模型配置 - 媒体模型（图像和视频）
 */

import { ASPECT_RATIO_OPTIONS } from '../types';
import { UnifiedModelConfig, ModelType, Provider, UnitType } from './unified-models';

/**
 * GRSAI 媒体模型配置
 */
export const GRSAI_MEDIA_MODELS: UnifiedModelConfig[] = [
  // 图像生成模型
  {
    id: 'flux-pro-1.1',
    name: '',
    translationKey: 'grsai.flux-pro-1.1',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 30,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'professional', 'flux_tech'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'gpt-4o-image',
    name: '',
    translationKey: 'grsai.gpt-4o-image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 40,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'gpt_powered', 'versatile'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        description: '生成数量',
        tooltip: '选择生成图片的数量（1-2张）',
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: [
          { value: '1024x1024', label: '正方形 (1024x1024)', description: '标准正方形尺寸' },
          { value: '1792x1024', label: '横屏 (1792x1024)', description: '宽屏尺寸' },
          { value: '1024x1792', label: '竖屏 (1024x1792)', description: '竖屏尺寸' }
        ],
        description: '图片尺寸',
        tooltip: '选择生成图片的具体尺寸',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-kontext-pro',
    name: '',
    translationKey: 'grsai.flux-kontext-pro',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 45,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'context_aware', 'professional'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择图片存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'sora-image',
    name: '',
    translationKey: 'grsai.sora-image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 50,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'high_quality', 'creative'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        description: '生成数量',
        tooltip: '选择生成图片的数量（1-2张）',
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: [
          { value: '1024x1024', label: '正方形 (1024x1024)', description: '标准正方形尺寸' },
          { value: '1792x1024', label: '横屏 (1792x1024)', description: '宽屏尺寸' },
          { value: '1024x1792', label: '竖屏 (1024x1792)', description: '竖屏尺寸' }
        ],
        description: '图片尺寸',
        tooltip: '选择生成图片的具体尺寸',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-pro-1.1-ultra',
    name: '',
    translationKey: 'grsai.flux-pro-1.1-ultra',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 60,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'ultra_quality', 'enhanced_flux'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-kontext-max',
    name: '',
    translationKey: 'grsai.flux-kontext-max',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 80,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'max_quality', 'context_aware'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        description: '图片宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '参考图片',
        tooltip: '上传参考图片，支持多张图片',
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择图片存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  // 视频生成模型
  {
    id: 'veo3-fast',
    name: '',
    translationKey: 'grsai.veo3-fast',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 100,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: '',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'fast', 'veo3_tech'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '首帧图片',
        tooltip: '上传首帧图片作为视频的起始画面',
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: [
          { value: '5', label: '5秒', description: '短视频，快速生成' },
          { value: '10', label: '10秒', description: '中等长度视频' }
        ],
        description: '视频时长',
        tooltip: '选择生成视频的时长',
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '720p',
        options: [
          { value: '720p', label: '720p (HD)', description: '高清分辨率，平衡质量和速度' },
          { value: '1080p', label: '1080p (Full HD)', description: '全高清分辨率，更高质量' }
        ],
        description: '视频分辨率',
        tooltip: '选择生成视频的分辨率',
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '24',
        options: [
          { value: '24', label: '24 FPS', description: '电影级帧率' },
          { value: '30', label: '30 FPS', description: '标准视频帧率' }
        ],
        description: '帧率',
        tooltip: '选择视频的帧率',
        group: 'advanced'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择视频存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'cdn'],
      expert: []
    }
  },

  {
    id: 'veo3-pro',
    name: '',
    translationKey: 'grsai.veo3-pro',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 200,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: '',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'professional', 'advanced_veo3'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        description: '首帧图片',
        tooltip: '上传首帧图片作为视频的起始画面',
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: [
          { value: '5', label: '5秒', description: '短视频，快速生成' },
          { value: '10', label: '10秒', description: '中等长度视频' },
          { value: '15', label: '15秒', description: '长视频，更丰富内容' }
        ],
        description: '视频时长',
        tooltip: '选择生成视频的时长',
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '1080p',
        options: [
          { value: '720p', label: '720p (HD)', description: '高清分辨率，快速生成' },
          { value: '1080p', label: '1080p (Full HD)', description: '全高清分辨率，标准质量' },
          { value: '4k', label: '4K (Ultra HD)', description: '超高清分辨率，最高质量' }
        ],
        description: '视频分辨率',
        tooltip: '选择生成视频的分辨率',
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '30',
        options: [
          { value: '24', label: '24 FPS', description: '电影级帧率' },
          { value: '30', label: '30 FPS', description: '标准视频帧率' },
          { value: '60', label: '60 FPS', description: '高帧率，更流畅' }
        ],
        description: '帧率',
        tooltip: '选择视频的帧率',
        group: 'advanced'
      },
      {
        name: 'quality',
        type: 'select',
        required: false,
        default: 'high',
        options: [
          { value: 'standard', label: '标准质量', description: '平衡质量和速度' },
          { value: 'high', label: '高质量', description: '更好的视觉效果' },
          { value: 'ultra', label: '超高质量', description: '最佳视觉效果，生成较慢' }
        ],
        description: '生成质量',
        tooltip: '选择视频生成的质量级别',
        group: 'advanced'
      },
      {
        name: 'motion_intensity',
        type: 'number',
        required: false,
        default: 5,
        min: 1,
        max: 10,
        description: '运动强度',
        tooltip: '控制视频中的运动幅度，1为静态，10为剧烈运动',
        group: 'expert'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: [
          { value: 'global', label: '全球CDN', description: '使用全球CDN加速' },
          { value: 'zh', label: '中国CDN', description: '使用中国CDN加速' }
        ],
        description: 'CDN选择',
        tooltip: '选择视频存储和访问的CDN节点',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'quality', 'cdn'],
      expert: ['motion_intensity']
    }
  }
];
