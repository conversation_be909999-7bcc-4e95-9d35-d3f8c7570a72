/**
 * 模型参数配置类型定义
 */

export interface ParameterConfig {
  name: string;
  type: 'number' | 'string' | 'select' | 'boolean' | 'range' | 'file';
  required: boolean;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{
    value: string;
    label?: string; // 可选，运行时从翻译文件获取
    description?: string; // 可选，运行时从翻译文件获取
  }>;
  description?: string; // 可选，运行时从翻译文件获取
  tooltip?: string; // 可选，运行时从翻译文件获取
  group: 'basic' | 'advanced' | 'expert';
  dependsOn?: string; // 依赖其他参数
  condition?: {
    field: string;
    value: any;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'not_empty';
  };
}

export interface ModelParameterConfig {
  modelId: string;
  version: string;
  provider: string;
  modelType: 'text' | 'image' | 'video' | 'multimodal';
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}

/**
 * 参数组标题映射 - 使用翻译key
 */
export const PARAMETER_GROUP_TITLES = {
  basic: 'common.parameterGroups.basic',
  advanced: 'common.parameterGroups.advanced',
  expert: 'common.parameterGroups.expert'
} as const;

/**
 * 通用的宽高比选项 - 使用翻译key
 */
export const ASPECT_RATIO_OPTIONS = [
  { value: '1:1', labelKey: 'common.options.aspectRatio.1:1.label', descriptionKey: 'common.options.aspectRatio.1:1.description' },
  { value: '16:9', labelKey: 'common.options.aspectRatio.16:9.label', descriptionKey: 'common.options.aspectRatio.16:9.description' },
  { value: '9:16', labelKey: 'common.options.aspectRatio.9:16.label', descriptionKey: 'common.options.aspectRatio.9:16.description' },
  { value: '4:3', labelKey: 'common.options.aspectRatio.4:3.label', descriptionKey: 'common.options.aspectRatio.4:3.description' },
  { value: '3:2', labelKey: 'common.options.aspectRatio.3:2.label', descriptionKey: 'common.options.aspectRatio.3:2.description' },
  { value: '21:9', labelKey: 'common.options.aspectRatio.21:9.label', descriptionKey: 'common.options.aspectRatio.21:9.description' },
  { value: '2:3', labelKey: 'common.options.aspectRatio.2:3.label', descriptionKey: 'common.options.aspectRatio.2:3.description' },
  { value: '4:5', labelKey: 'common.options.aspectRatio.4:5.label', descriptionKey: 'common.options.aspectRatio.4:5.description' },
  { value: '5:4', labelKey: 'common.options.aspectRatio.5:4.label', descriptionKey: 'common.options.aspectRatio.5:4.description' },
  { value: '3:4', labelKey: 'common.options.aspectRatio.3:4.label', descriptionKey: 'common.options.aspectRatio.3:4.description' }
];

/**
 * 通用的图片数量选项
 */
export const VARIANTS_OPTIONS = [
  { value: '1', label: '1张图片', description: '生成1张图片' },
  { value: '2', label: '2张图片', description: '生成2张图片' },
  { value: '3', label: '3张图片', description: '生成3张图片' },
  { value: '4', label: '4张图片', description: '生成4张图片' }
];

/**
 * 通用的输出格式选项
 */
export const OUTPUT_FORMAT_OPTIONS = [
  { value: 'webp', label: 'WebP', description: '现代格式，高压缩率' },
  { value: 'jpg', label: 'JPG', description: '通用格式，兼容性好' },
  { value: 'png', label: 'PNG', description: '无损压缩，文件较大' }
];

/**
 * 通用的图片尺寸选项（Replicate）
 */
export const MEGAPIXELS_OPTIONS = [
  { value: '0.25', label: '0.25 百万像素', description: '快速生成，较低分辨率' },
  { value: '1', label: '1 百万像素', description: '标准质量，平衡速度' }
];

/**
 * 通用的CDN选项 - 使用翻译key
 */
export const CDN_OPTIONS = [
  { value: 'global', labelKey: 'common.options.cdn.global.label', descriptionKey: 'common.options.cdn.global.description' },
  { value: 'zh', labelKey: 'common.options.cdn.zh.label', descriptionKey: 'common.options.cdn.zh.description' }
];

/**
 * 通用的布尔值选项
 */
export const BOOLEAN_OPTIONS = [
  { value: 'true', label: '启用', description: '开启此功能' },
  { value: 'false', label: '禁用', description: '关闭此功能' }
];
