/**
 * 翻译解析工具
 * 用于解析公共翻译和模型特定翻译
 */

import { ParameterConfig } from './types';

export interface TranslationData {
  common: {
    parameters: Record<string, {
      description: string;
      tooltip: string;
      options?: Record<string, {
        label: string;
        description: string;
      }>;
    }>;
    parameterGroups: Record<string, string>;
    options: Record<string, Record<string, {
      label: string;
      description: string;
    }>>;
    ui: Record<string, string>;
  };
  models: Record<string, {
    name: string;
    description: string;
    parameters?: Record<string, {
      description?: string;
      tooltip?: string;
      options?: Record<string, {
        label: string;
        description: string;
      }>;
    }>;
  }>;
}

/**
 * 解析参数翻译，优先使用模型特定翻译，回退到公共翻译
 */
export function resolveParameterTranslation(
  parameterName: string,
  modelTranslationKey: string,
  translations: TranslationData
): {
  description?: string;
  tooltip?: string;
} {
  // 优先使用模型特定翻译
  const modelTranslation = translations.models[modelTranslationKey];
  if (modelTranslation?.parameters?.[parameterName]) {
    const paramTranslation = modelTranslation.parameters[parameterName];
    return {
      description: paramTranslation.description,
      tooltip: paramTranslation.tooltip
    };
  }

  // 回退到公共翻译
  const commonTranslation = translations.common.parameters[parameterName];
  if (commonTranslation) {
    return {
      description: commonTranslation.description,
      tooltip: commonTranslation.tooltip
    };
  }

  return {};
}

/**
 * 解析选项翻译
 */
export function resolveOptionTranslation(
  optionValue: string,
  optionType: string,
  translations: TranslationData
): {
  label?: string;
  description?: string;
} {
  const optionTranslations = translations.common.options[optionType];
  if (optionTranslations?.[optionValue]) {
    return optionTranslations[optionValue];
  }

  return {};
}

/**
 * 解析参数组标题翻译
 */
export function resolveParameterGroupTitle(
  groupKey: string,
  translations: TranslationData
): string {
  return translations.common.parameterGroups[groupKey] || groupKey;
}

/**
 * 解析UI文本翻译
 */
export function resolveUIText(
  textKey: string,
  translations: TranslationData
): string {
  return translations.common.ui[textKey] || textKey;
}

/**
 * 应用翻译到参数配置
 */
export function applyTranslationToParameter(
  parameter: ParameterConfig,
  modelTranslationKey: string,
  translations: TranslationData
): ParameterConfig {
  const paramTranslation = resolveParameterTranslation(
    parameter.name,
    modelTranslationKey,
    translations
  );

  const translatedParameter: ParameterConfig = {
    ...parameter,
    description: paramTranslation.description || parameter.description,
    tooltip: paramTranslation.tooltip || parameter.tooltip
  };

  // 处理选项翻译
  if (parameter.options) {
    translatedParameter.options = parameter.options.map(option => {
      // 如果选项有 labelKey 和 descriptionKey，使用翻译解析
      if ('labelKey' in option && 'descriptionKey' in option) {
        const labelParts = (option as any).labelKey.split('.');
        const descriptionParts = (option as any).descriptionKey.split('.');
        
        if (labelParts.length >= 4 && labelParts[0] === 'common' && labelParts[1] === 'options') {
          const optionType = labelParts[2];
          const optionValue = labelParts[3];
          const optionTranslation = resolveOptionTranslation(optionValue, optionType, translations);
          
          return {
            value: option.value,
            label: optionTranslation.label || option.value,
            description: optionTranslation.description || ''
          };
        }
      }

      return option;
    });
  }

  return translatedParameter;
}

/**
 * 应用翻译到参数配置列表
 */
export function applyTranslationToParameters(
  parameters: ParameterConfig[],
  modelTranslationKey: string,
  translations: TranslationData
): ParameterConfig[] {
  return parameters.map(parameter => 
    applyTranslationToParameter(parameter, modelTranslationKey, translations)
  );
}
